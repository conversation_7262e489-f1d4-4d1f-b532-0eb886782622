package com.xh.vdm.alarm.entity;

import lombok.Data;

import java.util.List;

/**
 * 告警类型信息
 */
@Data
public class AlarmTypeInfo {

	//型号
	private String model;
	//终端名称
	private String name;
	//sos告警
	private List<AlarmTypeBean> sosAlarm;
	//终端运行状态告警
	private List<AlarmTypeBean> deviceAlarm;
	//业务告警
	private List<AlarmTypeBean> busiAlarm;

}


/**
 * 每个具体的告警类型信息
 */
@Data
class AlarmTypeBean{
	private String model;
	private String alarmCategory;
	private String alarmTypeCode;
	private String alarmTypeName;
}
