package com.xh.vdm.bd.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
public class LocationKuduWithStopPage {

	//查询到的数据总量
	private Long total;
	//实际返回的数据总量
	private Long realTotal;

	private String deptName;
	private Integer deviceCategory;
	private String deviceId;
	private String deviceNum;
	private Integer deviceType;
	private String deviceUniqueId;
	private String deviceModel;
	private Integer specificity;
	private String targetName;
	private Integer targetType;


	//处理过停靠点之后的数据总量
	private List<LocationKuduWithStopPoint> locations;
}
