package com.xh.vdm.bd.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description: 定位数据,带有停车点
 * @Author: zhouxw
 * @Date: 2023/5/23 19:12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LocationKuduWithStopPoint extends LocationKudu{

	//是否是停车点
	private Boolean isStopPoint;
	//停车开始时间
	private Long stopStartTime;
	//停车结束时间
	private Long stopEndTime;
	//停车时长
	private Long stopDuration;
}
