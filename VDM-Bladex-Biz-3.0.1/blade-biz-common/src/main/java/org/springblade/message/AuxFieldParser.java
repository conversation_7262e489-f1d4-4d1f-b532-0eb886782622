package org.springblade.message;

import lombok.Getter;

import javax.xml.bind.DatatypeConverter;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * 解析808中的拓展字段
 */
public class AuxFieldParser {
	@Getter
	public static class PositionAux {
		// Getters and Setters
		// 附加项id
		private byte id;
		private String idStr;
		// 附加项长度
		private byte len;
		// 附加项内容
		private Object value;

		public void setId(byte id) { this.id = id; }

		public void setIdStr(String idStr) { this.idStr = idStr; }

		public void setLen(byte len) { this.len = len; }

		public void setValue(Object value) { this.value = value; }
	}

	public static class PosAuxField {
		private Byte wireless;
		private Byte gnssNum;
		private Short ioState;
		private Byte charge;

		// Getters and Setters
		public Byte getWireless() { return wireless; }
		public void setWireless(Byte wireless) { this.wireless = wireless; }
		public Byte getGnssNum() { return gnssNum; }
		public void setGnssNum(Byte gnssNum) { this.gnssNum = gnssNum; }
		public Short getIoState() { return ioState; }
		public void setIoState(Short ioState) { this.ioState = ioState; }
		public Byte getCharge() { return charge; }
		public void setCharge(Byte charge) { this.charge = charge; }
	}

	public static PosAuxField getAuxField(String str) {
		PosAuxField field = new PosAuxField();
		PositionAux aux = new PositionAux();

		// 将16进制字符串转换为字节数组
		byte[] bts = DatatypeConverter.parseHexBinary(str);
		ByteBuffer buf = ByteBuffer.wrap(bts);
		buf.order(ByteOrder.BIG_ENDIAN); // 设置大端序

		while (buf.hasRemaining()) {
			// 附加项id
			aux.setId(buf.get());
			// 附加项长度
			aux.setLen(buf.get());
			// 附加项内容
			switch (aux.getId()) {
				case 0x30:
					field.setWireless(buf.get());
					break;
				case 0x31:
					field.setGnssNum(buf.get());
					break;
				case 0x2A:
					byte[] ioStateBytes = new byte[2];
					buf.get(ioStateBytes);
					field.setIoState((short)((ioStateBytes[0] & 0xFF) << 8 | (ioStateBytes[1] & 0xFF)));
					break;
				case (byte)0xF7:
					field.setCharge(buf.get());
					break;
				default:
					byte[] valueBytes = new byte[aux.getLen()];
					buf.get(valueBytes);
					aux.setValue(valueBytes);
			}
		}
		return field;
	}
}
