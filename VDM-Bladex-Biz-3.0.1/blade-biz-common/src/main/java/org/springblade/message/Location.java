package org.springblade.message;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

/**
 * @Description: 定位数据
 * @Author: zhouxw
 * @Date: 2023/5/23 19:12
 */
@Data
public class Location {

	private String id;
	private Long targetId;
	private Byte targetType;
	private Long deviceId;
	private String deviceNum;
	private Byte deviceType;
	private Double longitude;
	private Double latitude;
	private Integer altitude;
	private Float speed;
	private Short bearing;
	private Long time;
	private Integer status;
	private Integer alarm;
	private Float mileage;
	private Byte valid;
	private String auxiliary;
	private Long recvTime;
	private Byte batch;
	private Byte correction;
	@JsonProperty("locAddr")
	private String addr;
	@JsonProperty("posSys")
	private Byte posSystem;
	private Float realSpeed;
	private Float oilMass;
	private Short ioStatus;

	//acc状态
	private String accStr;
	//加速度计
	private String statusDsc;
	//无线通信网络信号强度
	private Byte Wireless;
	//用于定位的卫星数量
	private Byte gnssNum;
	//休眠状态0-无，1-深度休眠，2-休眠
	private Short ioState;
	//电量百分比
	private Byte charge;

}
