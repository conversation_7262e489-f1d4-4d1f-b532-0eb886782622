package com.xh.vdm.bd.utils;

import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 业务工具类
 */
@Component
@Slf4j
public class DictUtil {

	@Resource
	private IDictBizClient dictBizClient;


	/**
	 * 根据key获取字典map
	 * @param key
	 * @return
	 */
	public Map<String,String> getDictMap(String key){
		try{
			R<Map<String,String>> res = dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(key, "-1");
			if(!res.isSuccess()){
				log.error("调用字典接口失败");
				return null;
			}
			return res.getData();
		}catch (Exception e){
			log.error("查询字典接口失败",e);
			return null;
		}
	}

	/**
	 * 根据key和value获取字典map
	 * @param key
	 * @return
	 */
	public Map<String,String> getDictMap(String key,String value){
		try{
			R<Map<String,String>> res = dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(key, value);
			if(!res.isSuccess()){
				log.error("调用字典接口失败");
				return null;
			}
			return res.getData();
		}catch (Exception e){
			log.error("查询字典接口失败",e);
			return null;
		}
	}



}
