package com.xh.vdm.bd.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bd.mapper.BdmAbstractTargetMapper;
import com.xh.vdm.bd.service.IBdmAbstractTargetService;
import com.xh.vdm.biapi.entity.BdmAbstractTarget;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * (BdmAbstractTarget)表服务实现类
 */
@Service
public class BdmAbstractTargetServiceImpl extends ServiceImpl<BdmAbstractTargetMapper, BdmAbstractTarget> implements IBdmAbstractTargetService {


}
