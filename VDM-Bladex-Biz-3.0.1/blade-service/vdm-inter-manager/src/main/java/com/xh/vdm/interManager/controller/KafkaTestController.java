package com.xh.vdm.interManager.controller;

import com.alibaba.fastjson.JSON;
import com.xh.vdm.interManager.dto.message.PlatOrg;
import com.xh.vdm.interManager.dto.message.SyncPlatDataContainer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/kafka")
@Slf4j
public class KafkaTestController {

	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;


	@GetMapping("/test")
	public void test(){
		List<PlatOrg> platOrgs = new ArrayList<>();
		PlatOrg platOrg = new PlatOrg();
		platOrg.setName("土木工程组织");
		platOrg.setCode("83254322343");
		platOrg.setParentCode("1806154710923669506");
		platOrg.setFullName("土木工程组织全程名称");
		platOrg.setSort(3);
		platOrgs.add(platOrg);
		PlatOrg platOrg1 = new PlatOrg();
		platOrg1.setName("一个简称而已");
		platOrg1.setCode("827658432134");
		platOrg1.setParentCode("1806154710923669506");
		platOrg1.setFullName("我是全称，全称名称");
		platOrg1.setSort(6);
		platOrgs.add(platOrg1);
		SyncPlatDataContainer container = new SyncPlatDataContainer();
		container.setCode("1");    // 数据类型: 1-6
		container.setOpt("add");    // 操作类型: 1-3
		container.setData(platOrgs);    // 组织数据列表
		container.setTime(System.currentTimeMillis()); // 当前时间戳
		try {
			// 发送消息
			ListenableFuture<SendResult<String, String>> future =
				kafkaTemplate.send(CommonConstant.MESSAGE_PLAT_SYNC_TOPIC, JSON.toJSONString(container));

			// 同步等待结果（阻塞当前线程）
			SendResult<String, String> result = future.get();

			// 发送成功
			log.info("消息发送成功！");
			log.info("主题: " + result.getRecordMetadata().topic());
			log.info("分区: " + result.getRecordMetadata().partition());
			log.info("偏移量: " + result.getRecordMetadata().offset());
		} catch (Exception e) {
			// 发送失败
			System.err.println("消息发送失败: " + e.getMessage());
			e.printStackTrace();
		}
	}
}
