package com.xh.vdm.bi.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class TargetDeviceDTO {

	/**
	 * 監控对象ID
	 */
	@NotNull(message = "targetId不能为空")
	private Long targetId;
	/**
	 * 監控对象类型
	 */
	@NotNull(message = "targetType不能为空")
	private Integer targetType;

	//终端类别, 1-北斗定位终端，2-北斗穿戴式终端，3-北斗短报文终端，4-北斗监测终端，5-北斗授时终端
	private Integer deviceType;
	/**
	 * 監控对象名稱
	 */
	@NotNull(message = "targetName不能为空")
	private String targetName;
	/**
	 * 監控对象單位Id
	 */
	@NotNull(message = "deptId不能为空")
	private Long deptId;
	/**
	 * 序列号
	 */
	@NotNull(message = "uniqueId不能为空")
	private String uniqueId;

	//设备id
	private Long badId;
}
