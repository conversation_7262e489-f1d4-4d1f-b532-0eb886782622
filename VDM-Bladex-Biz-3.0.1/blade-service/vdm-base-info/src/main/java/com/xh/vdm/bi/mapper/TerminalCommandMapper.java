package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.entity.TerminalCommand;
import com.xh.vdm.bi.vo.request.PntDeviceRequest;
import com.xh.vdm.bi.vo.request.TerminalCommandRequest;
import com.xh.vdm.bi.vo.response.PntDeviceResponse;
import com.xh.vdm.bi.vo.response.TerminalCommandResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 终端指令信息表 Mapper 接口
 * </p>
 *
 * @since 2025-07-24
 */
@Mapper
public interface TerminalCommandMapper extends BaseMapper<TerminalCommand> {

	IPage<TerminalCommandResponse> queryAll(@Param("page") IPage page, @Param("request") TerminalCommandRequest request);
}
