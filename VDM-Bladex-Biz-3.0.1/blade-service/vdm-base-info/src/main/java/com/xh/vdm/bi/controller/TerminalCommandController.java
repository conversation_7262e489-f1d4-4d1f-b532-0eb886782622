package com.xh.vdm.bi.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.entity.TerminalCommand;
import com.xh.vdm.bi.service.TerminalCommandService;
import com.xh.vdm.bi.utils.AuthUtils;
import com.xh.vdm.bi.vo.request.TerminalCommandRequest;
import com.xh.vdm.bi.vo.response.PntDeviceResponse;
import com.xh.vdm.bi.vo.response.TerminalCommandResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.entity.DataAuthCE;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RestController
@RequestMapping("/terminal/command")
@Api(tags = "终端指令集配置管理")
@Slf4j
public class TerminalCommandController {

	@Resource
	private TerminalCommandService terminalCommandService;

	@Resource
	private CETokenUtil ceTokenUtil;



	/**
	 * 新增终端指令
	 */
	@PostMapping("/save")
	public R save(@RequestBody TerminalCommandRequest request) {
		try {
			Boolean b = terminalCommandService.saveTerminalCommand(request);
			if (b) {
				return R.data(ResultCode.SUCCESS.getCode(), "新增成功");
			}else {
				return R.fail(ResultCode.FAILURE.getCode(), "新增失败");
			}

		}catch (Exception e) {
			log.error("新增指令集配置失败",e.getMessage(), e);
			return R.fail("新增指令集配置失败！");
		}

	}

	/**
	 * 更新终端指令
	 */
	@PostMapping("/update")
	public R update(@RequestBody TerminalCommandRequest request) {
		try {
			Boolean b = terminalCommandService.updateByTerminalCommand(request);
			if (b) {
				return R.data(ResultCode.SUCCESS.getCode(), "修改成功");
			}else {
				return R.fail(ResultCode.FAILURE.getCode(), "修改失败");
			}
		}catch (Exception e){
			log.error("更新指令集配置失败",e.getMessage(), e);
			return R.fail("更新指令集配置失败！");
		}
	}

	/**
	 * 删除终端指令
	 */
	@PostMapping("/delete")
	public R delete(@PathVariable List<Long> ids) {
		try {
			Boolean delete = terminalCommandService.delete(ids);
			if (delete) {
				return R.data(ResultCode.SUCCESS.getCode(), "删除成功");
			}else {
				return R.fail(ResultCode.FAILURE.getCode(), "删除失败");
			}

		}catch (Exception e){
			log.error("删除指令集配置失败",e.getMessage(), e);
			return R.fail("删除指令集配置失败！");
		}
	}

	/**
	 * 根据ID查询终端指令
	 */
	@GetMapping("/id")
	@ApiOperation("根据ID查询终端指令")
	public R<TerminalCommandResponse> getById(@RequestParam Long id, BladeUser user) {
		TerminalCommandResponse response = new TerminalCommandResponse();
		try {
			TerminalCommand one = terminalCommandService.getOne(new LambdaQueryWrapper<TerminalCommand>().eq(TerminalCommand::getId, id));
			BeanUtil.copyProperties(one, response);
		}catch (Exception e) {
			log.error("查询指令集配置失败",e.getMessage(), e);
			return R.fail("查询指令集配置失败！");
		}
		return R.data(response);
	}

	/**
	 * 分页查询终端指令
	 */
	@PostMapping("/queryByPage")
	public R<IPage<TerminalCommandResponse>> queryByPage(@RequestBody TerminalCommandRequest request, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		IPage<TerminalCommandResponse> page = terminalCommandService.queryByPage(request, ceDataAuth);
		//enrichDataWithDict(page.getRecords());
		return R.data(page);
	}
}
