<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.TerminalCommandMapper">

    <select id="queryAll" resultType="com.xh.vdm.bi.vo.response.TerminalCommandResponse">
        SELECT
            id,
            model,
            firmware_version,
            protocol_version,
            command_content,
            command_desc,
            command_demo,
            create_account,
            create_time,
            update_account,
            update_time
        FROM bdm_terminal_command
        WHERE deleted = 0
        <if test="request.protocolVersion != null">
            and protocol_version like concat('%', #{request.protocolVersion}, '%')
        </if>
        <if test="request.model != null">
            and model like concat('%', #{request.model}, '%')
        </if>
        ORDER BY rd.create_time DESC
    </select>


</mapper>

