package com.xh.vdm.bi.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.entity.TerminalCommand;
import com.xh.vdm.bi.mapper.TerminalCommandMapper;
import com.xh.vdm.bi.service.TerminalCommandService;
import com.xh.vdm.bi.vo.request.TerminalCommandRequest;
import com.xh.vdm.bi.vo.response.TerminalCommandResponse;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class TerminalCommandServiceImpl extends ServiceImpl<TerminalCommandMapper, TerminalCommand> implements TerminalCommandService {

	@Resource
	private TerminalCommandMapper mapper;

	@Override
	public Boolean saveTerminalCommand(TerminalCommandRequest request) {
		TerminalCommand terminalCommand = new TerminalCommand();
		BeanUtil.copyProperties(request, terminalCommand);
		terminalCommand.setCreateAccount(AuthUtil.getUserAccount());
		terminalCommand.setCreateTime(new Date());
		int insert = mapper.insert(terminalCommand);
		if (insert > 0) {
			return true;
		}else  {
			return false;
		}
	}

	@Override
	public Boolean updateByTerminalCommand(TerminalCommandRequest request) {
		TerminalCommand terminalCommand = new TerminalCommand();
		BeanUtil.copyProperties(request, terminalCommand);
		terminalCommand.setUpdateAccount(AuthUtil.getUserAccount());
		terminalCommand.setUpdateTime(new Date());
		int insert = mapper.updateById(terminalCommand);
		if (insert > 0) {
			return true;
		}else  {
			return false;
		}
	}

	@Override
	public Boolean delete(List<Long> ids) {
		LambdaUpdateWrapper<TerminalCommand> updateWrapper = new LambdaUpdateWrapper<>();
		// 设置更新条件：id在指定列表中
		updateWrapper.in(TerminalCommand::getId, ids)
			// 更新deleted字段为1(已删除)
			.set(TerminalCommand::getDeleted, 1);
		int update = baseMapper.update(null, updateWrapper);
		if (update > 0) {
			return true;
		}else {
			return false;
		}
	}

	@Override
	public IPage<TerminalCommandResponse> queryByPage(TerminalCommandRequest request, DataAuthCE ceDataAuth) {
		Page page = new Page();
		page.setSize(request.getSize());
		page.setCurrent(request.getCurrent());
		return mapper.queryAll(page, request);
	}
}
