package com.xh.vdm.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 终端指令集配置表
 * </p>
 *
 * @since 2025-07-24
 */
@Data
@TableName("bdm_terminal_command")
@ApiModel(value="TerminalCommand对象", description="终端指令集配置表")
public class TerminalCommand implements Serializable {

	private static final long serialVersionUID = 1L;

	@TableId(value = "id", type = IdType.ASSIGN_ID)
	private Long id;

	@ApiModelProperty(value = "终端型号")
	private String model;

	@ApiModelProperty(value = "固件版本号")
	private String firmwareVersion;

	@ApiModelProperty(value = "通信协议版本号，取值：2011、2013、2019")
	private String protocolVersion;

	@ApiModelProperty(value = "指令内容")
	private String commandContent;

	@ApiModelProperty(value = "指令说明")
	private String commandDesc;

	@ApiModelProperty(value = "指令样例")
	private String commandDemo;

	@ApiModelProperty(value = "创建人")
	private String createAccount;

	@ApiModelProperty(value = "更新人")
	private String updateAccount;

	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	//0-未删除，1-已删除
	private Integer deleted;

}
