package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.entity.TerminalCommand;
import com.xh.vdm.bi.vo.request.TerminalCommandRequest;
import com.xh.vdm.bi.vo.response.TerminalCommandResponse;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * <p>
 * 终端指令信息表 服务类
 * </p>
 *
 * @since 2025-07-24
 */
public interface TerminalCommandService extends IService<TerminalCommand> {

	Boolean saveTerminalCommand(TerminalCommandRequest request);

	Boolean updateByTerminalCommand(TerminalCommandRequest request);

	Boolean delete(List<Long> ids);

	IPage<TerminalCommandResponse> queryByPage(TerminalCommandRequest request, DataAuthCE ceDataAuth);

}
