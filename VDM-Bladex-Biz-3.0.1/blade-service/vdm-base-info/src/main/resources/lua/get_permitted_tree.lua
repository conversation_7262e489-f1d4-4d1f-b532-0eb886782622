-- get_permitted_tree.lua (v4.0 - Hybrid-V2 Model)
-- 根据用户有权限的部门ID列表，构建权限树。
-- 部门节点的'total'和'onlineNum'，由其自身的'self*'字段和其所有子部门的'total'/'onlineNum'聚合而成。
-- 非部门节点（Category, Target）的总数由Java预先计算，此处仅作展示。

-- KEYS[1]: (unused)
-- KEYS[2...n]: 权限部门ID列表 (e.g., "dept_100")
-- ARGV: (未使用)

local node_key_prefix = "tree:node:"
local children_key_prefix = "tree:children:"
-- Redis内置cjson库，无需require

local build_node_subtree

-- 获取一个节点的完整静态子树（无聚合）
local function build_static_subtree(node_id)
    local node_key = node_key_prefix .. node_id
    local node_data_flat = redis.call("HGETALL", node_key)
    if #node_data_flat == 0 then return nil end

    local node = {}
    for i = 1, #node_data_flat, 2 do node[node_data_flat[i]] = node_data_flat[i+1] end

    if node["type"] == "device" then return node end

    local children_key = children_key_prefix .. node_id
    local children_ids = redis.call("ZRANGE", children_key, 0, -1)
    if #children_ids > 0 then
        node["children"] = {}
        for _, child_id in ipairs(children_ids) do
            local child_node = build_static_subtree(child_id)
            if child_node then table.insert(node["children"], child_node) end
        end
    end
    return node
end

-- 构建子树，根据节点类型决定是静态构建还是聚合构建
build_node_subtree = function(node_id)
    local node_key = node_key_prefix .. node_id
    local node_data_flat = redis.call("HGETALL", node_key)
    if #node_data_flat == 0 then return nil end

    local node = {}
    for i = 1, #node_data_flat, 2 do node[node_data_flat[i]] = node_data_flat[i+1] end

    if node["type"] == "dept" then
        node["total"] = tonumber(node["selfTotal"]) or 0
        node["onlineNum"] = tonumber(node["selfOnlineNum"]) or 0

        local children_key = children_key_prefix .. node_id
        local children_ids = redis.call("ZRANGE", children_key, 0, -1)
        if #children_ids > 0 then
            node["children"] = {}
            for _, child_id in ipairs(children_ids) do
                local child_subtree = build_node_subtree(child_id)
                if child_subtree then
                    if child_subtree["type"] == "dept" then
                        node["total"] = node["total"] + (tonumber(child_subtree["total"]) or 0)
                        node["onlineNum"] = node["onlineNum"] + (tonumber(child_subtree["onlineNum"]) or 0)
                    end
                    table.insert(node["children"], child_subtree)
                end
            end
        end
    else
        -- 对于Category/Target，我们信任Java预计算的结果，因此构建静态子树即可
        node = build_static_subtree(node_id)
    end
    return node
end

-- 主逻辑
local root_nodes = {}
for i = 2, #KEYS do
    local permitted_dept_id = KEYS[i]
    if permitted_dept_id then
        local root_node = build_node_subtree(permitted_dept_id)
        if root_node then
            table.insert(root_nodes, root_node)
        end
    end
end

return cjson.encode(root_nodes)
