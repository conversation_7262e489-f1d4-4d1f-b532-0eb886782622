--- update_device_status.lua

-- KEYS[1]: versionedNodeK<PERSON> (完整的版本化节点键，如 tree:v1:node:device_123 或 tree:v1:node:target_456)
-- ARGV[1]: newOnlineStatus (1 for online, 0 for offline)
-- ARGV[2]: currentVersion (当前版本号，如 v1)
--
-- atomically updates a device's or target's status and propagates the online count
-- up the tree hierarchy with version support.
--
-- Returns:
-- 1 if the status was updated.
-- 0 if the status was already the same or node not found.
-- -1 on error (e.g., node data is corrupt).

local versionedNodeKey = KEYS[1]
local newStatus = ARGV[1]
local currentVersion = ARGV[2]

-- 1. Check if node exists
if redis.call('EXISTS', versionedNodeKey) == 0 then
    return 0
end

-- 2. Get current online status and parentId
local nodeData = redis.call('HMGET', versionedNodeKey, 'online', 'parentId')
local currentStatus = nodeData[1]
local parentId = nodeData[2]

if not currentStatus then
    -- Node hash exists but 'online' field is missing.
    return 0 -- Or handle as an error
end

-- 3. If status is not changing, do nothing.
if currentStatus == newStatus then
    return 0
end

-- 4. Determine the increment value for online counts
local increment = (tonumber(newStatus) == 1) and 1 or -1

-- 5. Update the node's online status
redis.call('HSET', versionedNodeKey, 'online', newStatus)

-- 6. Traverse up the tree and update parent online counts
while parentId and parentId ~= 'null' and parentId ~= '' do
    -- Construct versioned parent key
    local versionedParentKey = "tree:" .. currentVersion .. ":node:" .. parentId

    if redis.call('EXISTS', versionedParentKey) == 0 then
        -- Parent node not found, stop propagation
        break
    end

    -- Increment the parent's online number
    redis.call('HINCRBY', versionedParentKey, 'onlineNum', increment)

    -- Get the next parent's ID
    parentId = redis.call('HGET', versionedParentKey, 'parentId')
end

return 1
