-- 批量保存节点数据的Lua脚本
-- KEYS: 无
-- ARGV: [1] = 节点数据JSON数组字符串

-- Redis内置cjson库，无需require
local node_key_prefix = "tree:node:"
local children_key_prefix = "tree:children:"
local creator_index_key_prefix = "tree:user_devices:"
local dept_devices_key_prefix = "tree:dept_devices:"
local cache_timeout = 86400 -- 24小时，单位秒

-- 解析节点数据
local nodes_json = ARGV[1]
local nodes = cjson.decode(nodes_json)

local saved_count = 0

for _, node in ipairs(nodes) do
    if node.id then
        -- 1. 保存节点自身
        local node_key = node_key_prefix .. node.id
        
        -- 将节点数据转换为hash格式
        for field, value in pairs(node) do
            if field ~= "children" then -- 排除children字段
                redis.call("HSET", node_key, field, tostring(value))
            end
        end
        redis.call("EXPIRE", node_key, cache_timeout)
        
        -- 2. 建立父子关系
        if node.parentId then
            local children_key = children_key_prefix .. node.parentId
            redis.call("ZADD", children_key, 0, node.id)
            redis.call("EXPIRE", children_key, cache_timeout)
        end
        
        -- 3. 如果是设备，建立索引
        if node.type == "device" then
            -- 创建者索引
            if node.createAccount then
                local creator_key = creator_index_key_prefix .. node.createAccount
                redis.call("SADD", creator_key, node.id)
                redis.call("EXPIRE", creator_key, cache_timeout)
            end
            
            -- 部门索引
            if node.deptId then
                local dept_key = dept_devices_key_prefix .. tostring(node.deptId)
                redis.call("SADD", dept_key, node.id)
                redis.call("EXPIRE", dept_key, cache_timeout)
            end
        end
        
        saved_count = saved_count + 1
    end
end

return saved_count
