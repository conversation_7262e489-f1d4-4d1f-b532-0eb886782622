package org.springblade.websocket.constant;


import org.springblade.websocket.dto.tree.NodeType;

/**
 * 树形结构缓存相关的常量
 */
public final class TreeCacheKeyConstant {

	private TreeCacheKeyConstant() {
		// private constructor to prevent instantiation
	}

	// region 基础定义 (Base Definitions)
	public static final String TREE_PREFIX = "tree";
	public static final String ROOT_DEPT_PARENT_ID = "0";
	public static final String VIRTUAL_ROOT_DEPT_KEY = NodeType.DEPT + "_" + ROOT_DEPT_PARENT_ID;
	public static final double DEFAULT_SORT_ORDER = 0.0;

	// region 版本化相关 (Versioning)
	/**
	 * 当前活跃版本的Redis key
	 */
	public static final String CURRENT_VERSION_KEY = TREE_PREFIX + ":current_version";

	/**
	 * 版本前缀
	 */
	public static final String VERSION_PREFIX = "v";

	/**
	 * 默认版本号
	 */
	public static final String DEFAULT_VERSION = VERSION_PREFIX + "1";

	public static final String BUILD_TREE_LOCK_KEY = "tree:lock:build";
	public static final String BUILD_DEVICE_LOCK_KEY = "tree:lock:device:";

	// region Prefixed Node ID Generators
	public static String getPrefixedDeptId(Long deptId) {
		return NodeType.DEPT + "_" + deptId;
	}

	public static String getPrefixedDeviceTypeId(Long deptId, Integer deviceType) {
		return NodeType.DEVICE_TYPE + "_" + deptId + "_" + deviceType;
	}

	public static String getPrefixedTargetId(String targetId) {
		return NodeType.TARGET + "_" + targetId;
	}

	public static String getPrefixedDeviceId(Long deviceId) {
		return NodeType.DEVICE + "_" + deviceId;
	}
	// endregion

	// region Key前缀 (Key Prefixes) - 版本化
	/**
	 * 获取版本化的节点前缀
	 * @param version 版本号，如 "v1", "v2"
	 * @return 版本化的节点前缀，如 "tree:v1:node:"
	 */
	public static String getVersionedNodePrefix(String version) {
		return TREE_PREFIX + ":" + version + ":node:";
	}

	/**
	 * 获取版本化的子节点前缀
	 * @param version 版本号，如 "v1", "v2"
	 * @return 版本化的子节点前缀，如 "tree:v1:children:"
	 */
	public static String getVersionedChildrenPrefix(String version) {
		return TREE_PREFIX + ":" + version + ":children:";
	}

	/**
	 * 获取版本化的用户设备前缀
	 * @param version 版本号，如 "v1", "v2"
	 * @return 版本化的用户设备前缀，如 "tree:v1:user_devices:"
	 */
	public static String getVersionedUserDevicesPrefix(String version) {
		return TREE_PREFIX + ":" + version + ":user_devices:";
	}

	/**
	 * 获取版本化的部门设备前缀
	 * @param version 版本号，如 "v1", "v2"
	 * @return 版本化的部门设备前缀，如 "tree:v1:dept_devices:"
	 */
	public static String getVersionedDeptDevicesPrefix(String version) {
		return TREE_PREFIX + ":" + version + ":dept_devices:";
	}

	// 兼容性：保留原有的非版本化前缀（将使用当前版本）
	public static final String NODE_PREFIX = TREE_PREFIX + ":node:";
	public static final String CHILDREN_PREFIX = TREE_PREFIX + ":children:";
	public static final String USER_DEVICES_PREFIX = TREE_PREFIX + ":user_devices:";
	public static final String DEPT_DEVICES_PREFIX = TREE_PREFIX + ":dept_devices:";
	// endregion

	// region Key模式 (Key Patterns for Scan/Delete) - 版本化
	/**
	 * 获取版本化的节点模式
	 * @param version 版本号
	 * @return 版本化的节点模式，如 "tree:v1:node:*"
	 */
	public static String getVersionedNodePattern(String version) {
		return getVersionedNodePrefix(version) + "*";
	}

	/**
	 * 获取版本化的子节点模式
	 * @param version 版本号
	 * @return 版本化的子节点模式，如 "tree:v1:children:*"
	 */
	public static String getVersionedChildrenPattern(String version) {
		return getVersionedChildrenPrefix(version) + "*";
	}

	/**
	 * 获取版本化的用户设备模式
	 * @param version 版本号
	 * @return 版本化的用户设备模式，如 "tree:v1:user_devices:*"
	 */
	public static String getVersionedUserDevicesPattern(String version) {
		return getVersionedUserDevicesPrefix(version) + "*";
	}

	/**
	 * 获取版本化的部门设备模式
	 * @param version 版本号
	 * @return 版本化的部门设备模式，如 "tree:v1:dept_devices:*"
	 */
	public static String getVersionedDeptDevicesPattern(String version) {
		return getVersionedDeptDevicesPrefix(version) + "*";
	}

	// 兼容性：保留原有的非版本化模式
	public static final String NODE_PATTERN = NODE_PREFIX + "*";
	public static final String CHILDREN_PATTERN = CHILDREN_PREFIX + "*";
	public static final String USER_DEVICES_PATTERN = USER_DEVICES_PREFIX + "*";
	public static final String DEPT_DEVICES_PATTERN = DEPT_DEVICES_PREFIX + "*";
	// endregion

	// region 方法特定Key (Method-Specific Keys) - 版本化
	/**
	 * 获取版本化的节点Key
	 * @param version 版本号
	 * @param prefixedNodeId 带前缀的节点ID
	 * @return 版本化的节点Key
	 */
	public static String getVersionedNodeKey(String version, String prefixedNodeId) {
		return getVersionedNodePrefix(version) + prefixedNodeId;
	}

	/**
	 * 获取版本化的子节点Key
	 * @param version 版本号
	 * @param prefixedParentNodeId 带前缀的父节点ID
	 * @return 版本化的子节点Key
	 */
	public static String getVersionedChildrenKey(String version, String prefixedParentNodeId) {
		return getVersionedChildrenPrefix(version) + prefixedParentNodeId;
	}

	/**
	 * 获取版本化的用户设备Key
	 * @param version 版本号
	 * @param userId 用户ID
	 * @return 版本化的用户设备Key
	 */
	public static String getVersionedUserDevicesKey(String version, String userId) {
		return getVersionedUserDevicesPrefix(version) + userId;
	}

	/**
	 * 获取版本化的部门设备Key
	 * @param version 版本号
	 * @param deptId 部门ID
	 * @return 版本化的部门设备Key
	 */
	public static String getVersionedDeptDevicesKey(String version, String deptId) {
		return getVersionedDeptDevicesPrefix(version) + deptId;
	}

	// 兼容性：保留原有的非版本化方法（将使用当前版本）
	public static String getNodeKey(String prefixedNodeId) {
		return NODE_PREFIX + prefixedNodeId;
	}

	public static String getChildrenKey(String prefixedParentNodeId) {
		return CHILDREN_PREFIX + prefixedParentNodeId;
	}

	public static String getUserDevicesKey(String userId) {
		return USER_DEVICES_PREFIX + userId;
	}

	public static String getDeptDevicesKey(String deptId) {
		return DEPT_DEVICES_PREFIX + deptId;
	}
	// endregion

	// region 缓存配置 (Cache Configurations)
	/**
	 * Redis缓存过期时间 (24小时)
	 */
	public static final long CACHE_EXPIRATION_SECONDS = 24 * 60 * 60;

	/**
	 * Pipeline写入Redis时的批次大小
	 */
	public static final int PIPELINE_BATCH_SIZE = 5000;

	/**
	 * Lua脚本清除Key时，单次SCAN的数量
	 */
	public static final String LUA_SCAN_COUNT = "1000";
	// endregion
}
