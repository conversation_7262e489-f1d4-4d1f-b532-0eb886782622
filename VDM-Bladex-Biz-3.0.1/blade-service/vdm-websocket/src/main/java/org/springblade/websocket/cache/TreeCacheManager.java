package org.springblade.websocket.cache;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * Redis缓存树操作核心管理器
 */
@Component
public class TreeCacheManager {

	@Resource
	private RedisTemplate<String, Object> redisTemplate;

	private static final String BUILD_TREE_LOCK_KEY = "tree:lock:build";
	private static final long LOCK_TIMEOUT_MINUTES = 10;

	/**
	 * 获取构建树的分布式锁
	 * @return true 如果获取锁成功
	 */
	public boolean acquireBuildLock() {
		return Boolean.TRUE.equals(
			redisTemplate.opsForValue().setIfAbsent(BUILD_TREE_LOCK_KEY, "building", LOCK_TIMEOUT_MINUTES, TimeUnit.MINUTES)
		);
	}

	/**
	 * 释放构建树的分布式锁
	 */
	public void releaseBuildLock() {
		redisTemplate.delete(BUILD_TREE_LOCK_KEY);
	}

}
