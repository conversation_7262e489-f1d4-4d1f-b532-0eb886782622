package com.xh.vdm.alarm.vo.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 终端告警配置
 */
@Data
public class TerminalAlarmConfigRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	//页码
	private Integer current;
	//页大小
	private Integer size;


	private Long id;
	/**
	 * 终端型号
	 */
	private String model;

	/**
	 * 终端名称
	 */
	private String name;

	/**
	 * 告警分类（如：紧急告警、业务告警和运行告警）
	 */
	private String alarmCategory;

	/**
	 * 告警编码（唯一标识）
	 */
	private String alarmCode;

	/**
	 * 告警名称
	 */
	private String alarmName;

	/**
	 * 状态 1:有效、0:无效
	 */
	private Integer status;

	/**
	 * 备注信息
	 */
	private String remark;

	/**
	 * 协议编码
	 */
	private String code;

	/**
	 * 标识位
	 */
	private String tag;

	/**
	 * 逻辑删除标志（0-未删除，1-已删除）
	 */
	private Integer deleted;

	/**
	 * 附加消息ID
	 */
	private String additionalMessageId;

	/**
	 * 附加消息长度
	 */
	private String additionalMessageLength;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 最后修改时间
	 */
	private Date updateTime;
}
