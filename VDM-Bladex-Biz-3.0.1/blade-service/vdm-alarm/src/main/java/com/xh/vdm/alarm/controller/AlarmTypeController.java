package com.xh.vdm.alarm.controller;

import com.xh.vdm.alarm.entity.AlarmTypeInfo;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 告警类型
 */
@RestController
@RequestMapping("/alarmType")
public class AlarmTypeController {


	/**
	 * 查询告警类型
	 * @return
	 */
	@GetMapping("/alarmTypeInfo")
	public R<List<AlarmTypeInfo>> alarmTypeInfo(){
		//1.查询全部有效的告警类型数据

		//2.组装数据

		return null;
	}


}
