package com.xh.vdm.alarm.vo.response;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xh.vdm.alarm.entity.AlarmTypeConfig;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class TerminalAlarmConfigResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;

	/**
	 * 终端型号
	 */
	private String model;

	/**
	 * 终端名称
	 */
	private String name;

	/**
	 * 告警分类（如：紧急告警、业务告警和运行告警）
	 */
	private String alarmCategory;

	/**
	 * 告警编码（唯一标识）
	 */
	private String alarmCode;

	/**
	 * 告警名称
	 */
	private String alarmName;

	/**
	 * 状态 1:有效、0:无效
	 */
	private Integer status;

	/**
	 * 备注信息
	 */
	private String remark;

	/**
	 * 协议编码
	 */
	private String code;

	/**
	 * 标识位
	 */
	private String tag;

	/**
	 * 附加消息ID
	 */
	private String additionalMessageId;

	/**
	 * 附加消息长度
	 */
	private String additionalMessageLength;


	/**
	 * 逻辑删除标志（0-未删除，1-已删除）
	 */
	private Integer deleted;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	/**
	 * 最后修改时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	/**
	 * DTO转Entity
	 */
	public static AlarmTypeConfig resToEntity(TerminalAlarmConfigResponse response) {
		if (response == null) {
			return null;
		}
		AlarmTypeConfig entity = new AlarmTypeConfig();
		BeanUtil.copyProperties(response, entity);
		return entity;
	}

	/**
	 * Entity转VO
	 */
	public static TerminalAlarmConfigResponse entityToRes(AlarmTypeConfig entity) {
		if (entity == null) {
			return null;
		}
		TerminalAlarmConfigResponse vo = new TerminalAlarmConfigResponse();
		BeanUtil.copyProperties(entity, vo);

		// 状态码转文本
//		vo.setStatusText(convertStatusToText(entity.getStatus()));

		return vo;
	}


}
