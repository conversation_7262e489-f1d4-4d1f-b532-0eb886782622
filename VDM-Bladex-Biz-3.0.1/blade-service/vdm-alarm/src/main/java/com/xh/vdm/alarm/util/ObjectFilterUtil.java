package com.xh.vdm.alarm.util;

import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 过滤集合中的空对象
 */
public class ObjectFilterUtil {

	/**
	 * 过滤集合，移除所有属性都为空的对象
	 * @param collection 待过滤的集合
	 * @return 过滤后的集合
	 */
	public static <T> List<T> filterEmptyObjects(Collection<T> collection) {
		if (collection == null || collection.isEmpty()) {
			return Collections.emptyList();
		}

		return collection.stream()
			.filter(ObjectFilterUtil::hasNonNullValue)
			.collect(Collectors.toList());
	}

	/**
	 * 判断对象是否有非空属性值
	 * @param obj 待检查的对象
	 * @return 如果对象有非空属性值返回true，否则返回false
	 */
	private static boolean hasNonNullValue(Object obj) {
		if (obj == null) {
			return false;
		}

		Class<?> clazz = obj.getClass();

		// 处理字符串
		if (obj instanceof String) {
			return !((String) obj).trim().isEmpty();
		}

		// 处理集合
		if (obj instanceof Collection) {
			Collection<?> collection = (Collection<?>) obj;
			return collection.stream().anyMatch(ObjectFilterUtil::hasNonNullValue);
		}

		// 处理Map
		if (obj instanceof Map) {
			Map<?, ?> map = (Map<?, ?>) obj;
			return map.values().stream().anyMatch(ObjectFilterUtil::hasNonNullValue);
		}

		// 处理数组
		if (clazz.isArray()) {
			if (Array.getLength(obj) == 0) {
				return false;
			}
			for (int i = 0; i < Array.getLength(obj); i++) {
				if (hasNonNullValue(Array.get(obj, i))) {
					return true;
				}
			}
			return false;
		}

		// 处理基本数据类型
		if (clazz.isPrimitive() || isWrapperType(clazz)) {
			return true;
		}

		// 处理自定义对象
		return checkFields(obj, clazz);
	}

	/**
	 * 检查对象的所有字段
	 */
	private static boolean checkFields(Object obj, Class<?> clazz) {
		// 遍历所有字段，包括父类字段
		while (clazz != Object.class) {
			for (Field field : clazz.getDeclaredFields()) {
				field.setAccessible(true);
				try {
					Object value = field.get(obj);
					if (hasNonNullValue(value)) {
						return true;
					}
				} catch (IllegalAccessException e) {
					// 忽略访问异常，继续检查其他字段
				}
			}
			clazz = clazz.getSuperclass();
		}
		return false;
	}

	/**
	 * 判断类是否为基本数据类型的包装类
	 */
	private static boolean isWrapperType(Class<?> clazz) {
		return clazz == Boolean.class ||
			clazz == Character.class ||
			clazz == Byte.class ||
			clazz == Short.class ||
			clazz == Integer.class ||
			clazz == Long.class ||
			clazz == Float.class ||
			clazz == Double.class;
	}
}
