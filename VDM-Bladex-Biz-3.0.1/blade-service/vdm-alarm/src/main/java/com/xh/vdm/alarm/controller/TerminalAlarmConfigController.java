package com.xh.vdm.alarm.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.alarm.dto.TerminalAlarmConfigExcelDTO;
import com.xh.vdm.alarm.entity.AlarmTypeConfig;
import com.xh.vdm.alarm.service.AlarmTypeConfigService;
import com.xh.vdm.alarm.vo.request.TerminalAlarmConfigRequest;
import com.xh.vdm.alarm.vo.response.TerminalAlarmConfigResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springblade.common.minio.MinioService;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 *  终端告警类型配置控制器
 */
@RestController
@RequestMapping("/alarm/config")
@Api(tags = " 终端告警类型配置管理")
@Slf4j
public class TerminalAlarmConfigController {


	@Resource
	private AlarmTypeConfigService configService;

	@Resource
	private MinioService minioService;

	@PostMapping("/save")
	@ApiOperation("新增 终端告警类型配置")
	public R save(@RequestBody TerminalAlarmConfigRequest req) {
		TerminalAlarmConfigResponse terminalAlarmConfigResponse;
		try {
			terminalAlarmConfigResponse = configService.saveConfig(req);
		}catch (Exception e) {
			log.error("保存数据失败",e.getMessage(),e);
			return R.fail(ResultCode.FAILURE, "新增终端告警类型配置失败");
		}

		return R.data(ResultCode.SUCCESS.getCode(), terminalAlarmConfigResponse.getId(), "新增成功");
	}

	@PostMapping("/update")
	@ApiOperation("更新终端告警类型配置")
	public R update(@RequestBody TerminalAlarmConfigRequest request) {
		TerminalAlarmConfigResponse terminalAlarmConfigResponse;
		try {
			terminalAlarmConfigResponse = configService.updateConfig(request);
		}catch (Exception e) {
			log.error("修改数据失败",e.getMessage(),e);
			return R.fail(ResultCode.FAILURE, "更新终端告警类型配置失败");
		}
		return R.data(ResultCode.SUCCESS.getCode(), terminalAlarmConfigResponse.getId(), "修改成功");
	}

	@PostMapping("/delete")
	@ApiOperation("删除终端告警类型配置")
	public R<T> delete(@RequestBody List<Long> ids) {
		Boolean bool;
		try {
			bool = configService.deleteByIds(ids);
		}catch (Exception e) {
			log.error("删除数据失败",e.getMessage(),e);
			return R.fail(ResultCode.FAILURE, "删除终端告警类型配置失败");
		}

		return R.status(bool);
	}

	@GetMapping("/id")
	@ApiOperation("根据ID查询终端告警类型配置")
	public R<TerminalAlarmConfigResponse> getById(@RequestParam Long id) {
		TerminalAlarmConfigResponse terminalAlarmConfigResponse;
		try {
			terminalAlarmConfigResponse = configService.queryById(id);
		}catch (Exception e) {
			log.error("查询数据失败",e.getMessage(),e);
			return R.fail(ResultCode.FAILURE, "根据ID查询终端告警类型配置失败");
		}
		return R.data(terminalAlarmConfigResponse);
	}

	@PostMapping("/queryByPage")
	@ApiOperation("分页查询终端告警类型配置列表")
	public R<IPage<AlarmTypeConfig>> queryByPage(@RequestBody TerminalAlarmConfigRequest request) {
		IPage<AlarmTypeConfig> page;
		try {
			page = this.configService.queryByPage(request);
		}catch (Exception e) {
			log.error("查询数据失败",e.getMessage(),e);
			return R.fail(ResultCode.FAILURE, "分页查询终端告警类型配置列表失败");
		}
		return R.data(page);
	}


	@PostMapping("/import")
	public R importExcel(@RequestParam("file") MultipartFile file) {
		// 初始化返回结果和数据列表
		List<TerminalAlarmConfigExcelDTO> dataList;
		try {
			// 1. 验证文件有效性并读取数据
			dataList = readExcelData(file);
			if (dataList.isEmpty()) {
				return R.fail("数据为空");
			}

			// 3. 处理导入业务逻辑
			Map<List<TerminalAlarmConfigResponse>, List<TerminalAlarmConfigExcelDTO>> resultMap
				= configService.importExcel(dataList);

			// 4. 处理导入结果
			return processImportResult(resultMap);

		} catch (Exception e) {
			log.error("导入存量终端数据异常"+e.getMessage(), e);
			return R.fail(ResultCode.FAILURE,"导入存量终端失败");
		}

	}


	/**
	 * 处理导入结果
	 */
	private R processImportResult(Map<List<TerminalAlarmConfigResponse>, List<TerminalAlarmConfigExcelDTO>> resultMap) {
		if (resultMap.isEmpty()) {
			return R.fail("导入处理结果为空");
		}

		Map.Entry<List<TerminalAlarmConfigResponse>, List<TerminalAlarmConfigExcelDTO>> firstEntry
			= resultMap.entrySet().iterator().next();
		List<TerminalAlarmConfigResponse> firstKey = firstEntry.getKey();
		List<TerminalAlarmConfigExcelDTO> firstValue = firstEntry.getValue();

		if (firstKey.size() == firstValue.size()) {
			return R.data(ResultCode.SUCCESS.getCode(), "导入成功");
		}
		String menu = "终端告警类型配置管理";
		try {
			String filePath = minioService.exportToMinIO(menu, firstValue, TerminalAlarmConfigExcelDTO.class);
			return R.data(207, filePath);
		} catch (Exception e) {
			log.error("上传导入错误数据失败",e.getMessage() ,e);
			return R.fail(ResultCode.FAILURE.getCode(),"上传导入错误数据失败");
		}
	}


	/**
	 * 读取Excel文件数据
	 */
	private List<TerminalAlarmConfigExcelDTO> readExcelData(MultipartFile file) throws IOException {
		List<TerminalAlarmConfigExcelDTO> dataList = new ArrayList<>();

		try (InputStream inputStream = file.getInputStream();
			 Workbook workbook = new XSSFWorkbook(inputStream)) {
			Sheet sheet = workbook.getSheetAt(0);
			// 验证表格是否为空
			if (sheet.getLastRowNum() == 0) {
				log.info("表格不能为空");
				throw new RuntimeException("表格不能为空");
			}

			// 读取表头并验证必要列
			Map<String, Integer> headerMap = readHeader(sheet.getRow(0));
			validateRequiredColumns(headerMap);
			// 从第2行开始读取数据
			for (int i = 1; i <= sheet.getLastRowNum(); i++) {
				Row row = sheet.getRow(i);
				if (row == null) continue;

				TerminalAlarmConfigExcelDTO data = new TerminalAlarmConfigExcelDTO();
				populateDataFromRow(row, headerMap, data);
				dataList.add(data);
			}
		}

		return dataList;
	}

	/**
	 * 从行数据填充对象
	 */
	private void populateDataFromRow(Row row, Map<String, Integer> headerMap, TerminalAlarmConfigExcelDTO data) {
		// 读取终端型号
		setCellValue(row, headerMap, "终端型号", data::setModel);

		// 读取告警名称
		setCellValue(row, headerMap, "告警名称", data::setAlarmName);

		// 读取告警分类
		setCellValue(row, headerMap, "告警分类", data::setAlarmCategory);

		// 读取告警编码
		setCellValue(row, headerMap, "告警编码", data::setAlarmCode);

		// 附加消息ID
		setCellValue(row, headerMap, "附加消息ID", data::setAdditionalMessageId);

		// 读取附加消息长度
		setCellValue(row, headerMap, "经度", data::setAdditionalMessageLength);

		// 读取协议编码
		setCellValue(row, headerMap, "协议编码", data::setCode);

		// 读取标识位
		setCellValue(row, headerMap, "标识位", data::setTag);

		// 读取备注
		setCellValue(row, headerMap, "备注", data::setRemark);
	}

	/**
	 * 设置数值类型单元格值
	 */
	private <T> void setNumericCellValue(Row row, Map<String, Integer> headerMap, String columnName, Consumer<Double> setter) {
		Integer index = headerMap.get(columnName);
		if (index != null) {
			Cell cell = row.getCell(index);
			if (cell != null) {
				try {
					setter.accept(cell.getNumericCellValue());
				} catch (IllegalStateException e) {
					String value = getCellValueAsString(cell);
					if (value != null && !value.isEmpty()) {
						try {
							setter.accept(Double.parseDouble(value));
						} catch (NumberFormatException ex) {
							// 保持默认值，不做处理
						}
					}
				}
			}
		}
	}


	/**
	 * 设置整数类型单元格值
	 */
	private <T> void setIntegerCellValue(Row row, Map<String, Integer> headerMap, String columnName, Consumer<Integer> setter) {
		Integer index = headerMap.get(columnName);
		if (index != null) {
			Cell cell = row.getCell(index);
			if (cell != null) {
				try {
					setter.accept((int) cell.getNumericCellValue());
				} catch (IllegalStateException e) {
					String value = getCellValueAsString(cell);
					if (value != null && !value.isEmpty()) {
						try {
							setter.accept(Integer.parseInt(value));
						} catch (NumberFormatException ex) {
							// 保持默认值，不做处理
						}
					}
				}
			}
		}
	}


	/**
	 * 设置字符串类型单元格值
	 */
	private <T> void setCellValue(Row row, Map<String, Integer> headerMap, String columnName, Consumer<String> setter) {
		Integer index = headerMap.get(columnName);
		if (index != null) {
			String value = getCellValueAsString(row.getCell(index));
			setter.accept(value);
		}
	}

	/**
	 * 验证必要列是否存在
	 */
	private R validateRequiredColumns(Map<String, Integer> headerMap) {
		// 定义必要列及其错误提示
		Map<String, String> requiredColumns = new HashMap<>();
		requiredColumns.put("终端型号", "Excel文件缺少必要的列（终端型号）");
		requiredColumns.put("告警名称", "Excel文件缺少必要的列（告警名称）");
		requiredColumns.put("告警分类", "Excel文件缺少必要的列（告警分类）");
		requiredColumns.put("告警编码", "Excel文件缺少必要的列（告警编码）");
		requiredColumns.put("附加消息ID", "Excel文件缺少必要的列（附加消息ID）");
		requiredColumns.put("附加消息长度", "Excel文件缺少必要的列（附加消息长度）");
		requiredColumns.put("协议编码", "Excel文件缺少必要的列（协议编码）");
		requiredColumns.put("标识位", "Excel文件缺少必要的列（标识位）");
		requiredColumns.put("备注", "Excel文件缺少必要的列（备注）");

		// 检查必要列是否存在
		for (Map.Entry<String, String> entry : requiredColumns.entrySet()) {
			if (!headerMap.containsKey(entry.getKey())) {
				return R.fail(entry.getValue());
			}
		}
		return R.success("校验通过");
	}

	/**
	 * 读取Excel表头
	 */
	private Map<String, Integer> readHeader(Row headerRow) {
		Map<String, Integer> headerMap = new HashMap<>();
		for (int i = 0; i < headerRow.getLastCellNum(); i++) {
			String header = getCellValueAsString(headerRow.getCell(i));
			headerMap.put(header, i);
		}
		return headerMap;
	}

	private String getCellValueAsString(Cell cell) {
		if (cell == null) return "";

		switch (cell.getCellType()) {
			case STRING:
				return cell.getStringCellValue();
			case NUMERIC:
				if (DateUtil.isCellDateFormatted(cell)) {
					return cell.getDateCellValue().toString();
				} else {
					return String.valueOf((int) cell.getNumericCellValue());
				}
			case BOOLEAN:
				return String.valueOf(cell.getBooleanCellValue());
			case FORMULA:
				return cell.getCellFormula();
			default:
				return "";
		}
	}
}
