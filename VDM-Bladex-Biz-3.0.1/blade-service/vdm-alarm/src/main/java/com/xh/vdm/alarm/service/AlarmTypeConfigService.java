package com.xh.vdm.alarm.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.alarm.dto.TerminalAlarmConfigExcelDTO;
import com.xh.vdm.alarm.entity.AlarmTypeConfig;
import com.xh.vdm.alarm.vo.request.TerminalAlarmConfigRequest;
import com.xh.vdm.alarm.vo.response.TerminalAlarmConfigResponse;

import java.util.List;
import java.util.Map;

/**
 * 终端告警配置Service接口
 */
public interface AlarmTypeConfigService extends IService<AlarmTypeConfig> {

	TerminalAlarmConfigResponse saveConfig(TerminalAlarmConfigRequest req);


	TerminalAlarmConfigResponse updateConfig(TerminalAlarmConfigRequest req);


	Boolean deleteByIds(List<Long> ids);

	TerminalAlarmConfigResponse queryById(Long id);


	IPage<AlarmTypeConfig> queryByPage(TerminalAlarmConfigRequest request);

	Map<List<TerminalAlarmConfigResponse>, List<TerminalAlarmConfigExcelDTO>> importExcel(List<TerminalAlarmConfigExcelDTO> dataList);
}
