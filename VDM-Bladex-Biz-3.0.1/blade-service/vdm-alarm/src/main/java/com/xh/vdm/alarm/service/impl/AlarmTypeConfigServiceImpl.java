package com.xh.vdm.alarm.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.alarm.dto.TerminalAlarmConfigExcelDTO;
import com.xh.vdm.alarm.entity.AlarmTypeConfig;
import com.xh.vdm.alarm.enums.AlarmCategoryEnum;
import com.xh.vdm.alarm.mapper.AlarmTypeConfigMapper;
import com.xh.vdm.alarm.service.AlarmTypeConfigService;
import com.xh.vdm.alarm.util.ObjectFilterUtil;
import com.xh.vdm.alarm.vo.request.TerminalAlarmConfigRequest;
import com.xh.vdm.alarm.vo.response.TerminalAlarmConfigResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 终端告警配置Service实现类
 */
@Service
public class AlarmTypeConfigServiceImpl extends ServiceImpl<AlarmTypeConfigMapper, AlarmTypeConfig> implements AlarmTypeConfigService {

	@Resource
	private AlarmTypeConfigMapper alarmTypeConfigMapper;

	@Override
	public TerminalAlarmConfigResponse saveConfig(TerminalAlarmConfigRequest req) {
		AlarmTypeConfig config = new AlarmTypeConfig();
		BeanUtil.copyProperties(req, config);
		config.setCreateTime(new Date());
		config.setUpdateTime(new Date());
		config.setDeleted(0);
		alarmTypeConfigMapper.insert(config);
		TerminalAlarmConfigResponse terminalAlarmConfigResponse = TerminalAlarmConfigResponse.entityToRes(config);
		return terminalAlarmConfigResponse;
	}

	@Override
	public TerminalAlarmConfigResponse updateConfig(TerminalAlarmConfigRequest req) {
		AlarmTypeConfig config = new AlarmTypeConfig();
		BeanUtil.copyProperties(req, config);
		config.setUpdateTime(new Date());
		alarmTypeConfigMapper.updateById(config);
		TerminalAlarmConfigResponse terminalAlarmConfigResponse = TerminalAlarmConfigResponse.entityToRes(config);
		return terminalAlarmConfigResponse;
	}

	@Override
	public Boolean deleteByIds(List<Long> ids) {
		// 创建更新条件构造器
		UpdateWrapper<AlarmTypeConfig> updateWrapper = new UpdateWrapper<>();
		// 设置更新条件：id 在指定的 ids 集合中
		updateWrapper.in("id", ids);
		// 设置要更新的字段：deleted = 1
		updateWrapper.set("deleted", 1);
		// 执行批量更新操作
		int i = alarmTypeConfigMapper.update(null, updateWrapper);
		if (i > 0) {
			return true;
		}
		return false;
	}

	@Override
	public TerminalAlarmConfigResponse queryById(Long id) {
		AlarmTypeConfig config = alarmTypeConfigMapper.selectById(id);
		return TerminalAlarmConfigResponse.entityToRes(config);
	}

	@Override
	public IPage<AlarmTypeConfig> queryByPage(TerminalAlarmConfigRequest request) {
		Page<AlarmTypeConfig> page = new Page<>(request.getCurrent(), request.getSize());
		BeanUtil.copyProperties(request,new TerminalAlarmConfigRequest());
		LambdaQueryWrapper<AlarmTypeConfig> wrapper = new LambdaQueryWrapper<>();
		// 添加查询条件
		if (StringUtils.isNotBlank(request.getModel())) {
			wrapper.like(AlarmTypeConfig::getModel, request.getModel());
		}
		if (StringUtils.isNotBlank(request.getCode())) {
			wrapper.eq(AlarmTypeConfig::getCode, request.getCode());
		}
		if (StringUtils.isNotBlank(request.getName())) {
			wrapper.like(AlarmTypeConfig::getName, request.getName());
		}
		if (StringUtils.isNotBlank(request.getAlarmCategory())) {
			wrapper.eq(AlarmTypeConfig::getAlarmCategory, request.getAlarmCategory());
		}
		if (StringUtils.isNotBlank(request.getAlarmName())) {
			wrapper.like(AlarmTypeConfig::getAlarmName, request.getAlarmName());
		}
		wrapper.eq(AlarmTypeConfig::getDeleted, 0);
		// 按创建时间降序排列
		wrapper.orderByDesc(AlarmTypeConfig::getCreateTime);
		Page<AlarmTypeConfig> terminalAlarmConfigPage = this.page(page, wrapper);
		// 执行分页查询
		return terminalAlarmConfigPage;
	}

	@Override
	public Map<List<TerminalAlarmConfigResponse>, List<TerminalAlarmConfigExcelDTO>> importExcel(List<TerminalAlarmConfigExcelDTO> list) {
		Map<List<TerminalAlarmConfigResponse>, List<TerminalAlarmConfigExcelDTO>> map = new HashMap<>();
		// 过滤空对象
		List<TerminalAlarmConfigExcelDTO> filteredList = ObjectFilterUtil.filterEmptyObjects(list);
		//校验导入数据的合法性
		List<TerminalAlarmConfigExcelDTO> duplicateRequests = getDuplicateRequests(filteredList);
		List<TerminalAlarmConfigExcelDTO> requestList = duplicateRequests.stream()
			.filter(item -> item.getMsg() == null || item.getMsg().trim().isEmpty())
			.collect(Collectors.toList());
		List<TerminalAlarmConfigResponse> existingTerminalRequestList = new ArrayList<>();
		if (duplicateRequests.size() == requestList.size()) {
			enrichDataWithDict(requestList);
			for (TerminalAlarmConfigExcelDTO dto : requestList) {
				TerminalAlarmConfigResponse terminalAlarmConfigResponse = new TerminalAlarmConfigResponse();
				AlarmTypeConfig alarmTypeConfig = new AlarmTypeConfig();
				BeanUtil.copyProperties(dto, alarmTypeConfig);
				alarmTypeConfig.setDeleted(0);
				alarmTypeConfigMapper.insert(alarmTypeConfig);
				BeanUtil.copyProperties(alarmTypeConfig, terminalAlarmConfigResponse);
				existingTerminalRequestList.add(terminalAlarmConfigResponse);
			}
		}
		map.put(existingTerminalRequestList, duplicateRequests);
		return map;
	}


	private void enrichDataWithDict(List<TerminalAlarmConfigExcelDTO> records) {
		if (records.isEmpty()) {
			return;
		}

		Map<String, String> classMap = new HashMap<>();
		for (AlarmCategoryEnum type : AlarmCategoryEnum.values()) {
			classMap.put(type.getLabel(), type.getValue());
		}
		for (TerminalAlarmConfigExcelDTO response : records) {
			response.setAlarmCategory(classMap.getOrDefault(String.valueOf(response.getAlarmCategory()), null));
		}
	}

	/**
	 * 校验
	 *
	 * @param list
	 * @return
	 */
	public List<TerminalAlarmConfigExcelDTO> getDuplicateRequests(List<TerminalAlarmConfigExcelDTO> list) {
		//必填字段必须有值
		for (TerminalAlarmConfigExcelDTO request : list) {
			if (StringUtils.isEmpty(request.getAlarmCode())) {
				if (request.getMsg() != null && !request.getMsg().isEmpty()) {
					request.setMsg(request.getMsg() + "；告警编码不能为空");
				} else {
					request.setMsg("告警编码不能为空");
				}
			}
			if (StringUtils.isEmpty(request.getModel())) {
				if (request.getMsg() != null && !request.getMsg().isEmpty()) {
					request.setMsg(request.getMsg() + "；终端型号不能为空");
				} else {
					request.setMsg("终端型号不能为空");
				}
			}

			if (StringUtils.isEmpty(request.getAlarmName())) {
				if (request.getMsg() != null && !request.getMsg().isEmpty()) {
					request.setMsg(request.getMsg() + "；告警名称不能为空");
				} else {
					request.setMsg("告警名称不能为空");
				}
			}
			if (StringUtils.isEmpty(request.getAlarmName())) {
				if (request.getMsg() != null && !request.getMsg().isEmpty()) {
					request.setMsg(request.getMsg() + "；告警名称不能为空");
				} else {
					request.setMsg("告警名称不能为空");
				}
			}
		}
		return list;
	}
}
