package com.xh.vdm.alarm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 终端告警类型配置实体类
 */
@Data
@TableName("bdm_alarm_type_config")
public class AlarmTypeConfig implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 终端型号
	 */
	private String model;

	/**
	 * 终端名称
	 */
	private String name;

	/**
	 * 告警分类（如：紧急告警、业务告警和运行告警）
	 */
	private String alarmCategory;

	/**
	 * 告警编码（唯一标识）
	 */
	private String alarmCode;

	/**
	 * 告警名称
	 */
	private String alarmName;

	/**
	 * 状态 1:有效、0:无效
	 */
	private Integer status;

	/**
	 * 备注信息
	 */
	private String remark;

	/**
	 * 协议编码
	 */
	private String code;

	/**
	 * 标识位
	 */
	private String tag;

	/**
	 * 附加消息ID
	 */
	private String additionalMessageId;

	/**
	 * 附加消息长度
	 */
	private String additionalMessageLength;


	/**
	 * 逻辑删除标志（0-未删除，1-已删除）
	 */
	private Integer deleted;

	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 最后修改时间
	 */
	private Date updateTime;


}
