package com.xh.vdm.bdCheck.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


import java.io.Serializable;
import java.util.Date;

/**
 * 来自赋码机的赋码结果同步至平台 接口 发生异常记录日志
 */
@Data
@TableName("terminal_synchronization_log")
public class TerminalSynchronizationLog implements Serializable {


	private static final long serialVersionUID = 1L;

	/**
	 * 主键ID
	 */
	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	/**
	 * 设备赋码值
	 */
	private String deviceNum;

	/**
	 * 设备赋码签名
	 */
	private String deviceNumSign;

	/**
	 * 设备类型（B：基础设备，N：导航定位设备，C：短报文通信设备，T：授时设备，D：探测监测设备，M：定位模组）
	 */
	private String deviceType;

	/**
	 * 设备编号(终端编号)
	 */
	private String deviceNo;

	/**
	 * 设备序列号
	 */
	private String deviceSeq;

	/**
	 * 北斗芯片序列号
	 */
	private String chipSeq;

	/**
	 * imei号
	 */
	private String imei;

	/**
	 * 设备厂商
	 */
	private String manufacturer;

	/**
	 * 设备型号
	 */
	private String deviceModel;

	/**
	 * 批次号
	 */
	private String batchNo;

	/**
	 * 所属公司ID
	 */
	private String companyId;

	/**
	 * 入网状态（0：未正式入网，1：已正式入网）
	 */
	private String formal;

	/**
	 * 赋码结果（0：未赋码，1：成功，2：失败）
	 */
	private String codeResult;

	/**
	 * 赋码时间
	 */
	private String codeTime;

	/**
	 * 赋码所使用的赋码机
	 */
	private String codeMachine;

	/**
	 * 设备描述信息
	 */
	private String description;

	/**
	 * 创建时间
	 */
	private Date createTime;

	//版本
	private String version;

}



