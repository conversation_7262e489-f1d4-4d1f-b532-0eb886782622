package com.xh.vdm.bdCheck.config;

import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RefreshScope
public class GJDMinioConfig {


	@Value("${minioGJD.Endpoint}")
	private String endpoint;

	@Value("${minioGJD.AccessKey}")
	private String accessKey;

	@Value("${minioGJD.SecretKey}")
	private String secretKey;

	@Value("${minioGJD.proxy-prefix}")
	private String minioProxyPrefix;


	@Bean
	public MinioClient gjdMinioClient() {
		return MinioClient.builder()
			.endpoint(endpoint)
			.credentials(accessKey, secretKey)
			.build();
	}
}
